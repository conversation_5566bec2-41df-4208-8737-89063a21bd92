import { ArrowDown } from "lucide-react";

export const HeroSection = () => {
  return (
    <section
      id="hero"
      className="relative min-h-screen flex flex-col items-center justify-center px-4"
    >
      <div className="container max-w-4xl mx-auto text-center z-10">
        <div className="space-y-6">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            <span className="opacity-0 animate-fade-in"> Hi, I Am</span>
            <span className="text-blue-500 opacity-0 animate-fade-in-delay-1">
              {" "}
              Ahmet Utku
            </span>
            <span className="text-blue-500 ml-2 opacity-0 animate-fade-in-delay-2">
              {" "}
              Pelen
            </span>
          </h1>

          <p className="text-2xl md:text-xl text-muted-foreground max-2-2xl mx-auto opacity-0 animate-fade-in-delay-3">
            I create Web Sites and Web Applications with modern technologies.
            <br/>
            I am passionate about solving complex problems and facing new challenges while caring about the user experience and clean code
          </p>

          <div className="pt-4 opacity-0 animate-fade-in-delay-4">
            <a href="#projects" className="cosmic-button bg-blue-500 p-3">
              CHECK MY WORK
            </a>
          </div>
        </div>
      </div>

      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center animate-bounce">
        <span className="text-sm text-muted-foreground mb-2"> Scroll </span>
        <ArrowDown className="h-5 w-5 text-primary" />
      </div>
    </section>
  );
};
