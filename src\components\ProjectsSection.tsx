import { ArrowR<PERSON>, ExternalLink, Github } from "lucide-react";
import Image from "next/image";

const projects = [
  {
    id: 1,
    title: "Dummy Project Title",
    description: "A beautiful project done by using React , Tailwind and Fast API",
    image: "data:image/png;base64,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",
    tags: ["React", "TailwindCSS", "Fast API"],
    demoUrl: "#",
    githubUrl: "#",
  },
  {
    id: 2,
    title: "A Restaurant Website",
    description:
      "A beautiful Restaurant Website done using React , Tailwind which is responsive and allows you to order food online and book a table.",
    image: "data:image/png;base64,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",
    tags: ["TypeScript", "React JS", "TailwindCSS"],
    demoUrl: "#",
    githubUrl: "#",
  },
  {
    id: 3,
    title: "E-commerce Platform",
    description:
      "A Full Stack E-Commerve project done using Django , Javascript , Bootstrap which is mobile friendly",
    image: "data:image/png;base64,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",
    tags: ["Django", "Python", "Javascript"],
    demoUrl: "#",
    githubUrl: "#", 
  },
];

export const ProjectsSection = () => {
  return (
    <section id="projects" className="py-24 px-4 relative">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">
          {" "}
          FEATURED <span className="text-blue-500"> PROJECTS </span>
        </h2>

        <p className="text-center text-muted-foreground mb-12 max-w-2xl mx-auto">
          Here are some of my recent projects. Each project was carefully
          crafted with attention to detail, performance, and user experience.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, key) => (
            <div
              key={key}
              className="group bg-card rounded-lg overflow-hidden shadow-xs card-hover"
            >
              <div className="h-48 overflow-hidden">
                <Image
                  src={project.image}
                  alt={project.title}
                  width={500}
                  height={300}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
              </div>

              <div className="p-6">
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.tags.map((tag) => (
                    <span 
                      key={tag}
                      className="px-2 py-1 text-xs font-medium border rounded-full bg-secondary text-secondary-foreground"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <h3 className="text-xl font-semibold mb-1"> {project.title}</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  {project.description}
                </p>
                <div className="flex justify-between items-center">
                  <div className="flex space-x-3">
                    <a
                      href={project.demoUrl}
                      target="_blank"
                      className="text-foreground/80 hover:text-primary transition-colors duration-300"
                    >
                      <ExternalLink size={20} />
                    </a>
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      className="text-foreground/80 hover:text-primary transition-colors duration-300"
                    >
                      <Github size={20} />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <a
            className="cosmic-button w-fit flex items-center mx-auto gap-2"
            target="_blank"
            href="https://github.com/AhmetUtkuPelen"
          >
            Check My Github <ArrowRight size={16} />
          </a>
        </div>
      </div>
    </section>
  );
};
