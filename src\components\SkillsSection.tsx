'use client'

import { cn } from "@/utils/utils";
import { useState } from "react";
import { FaHtml5 } from "react-icons/fa";
import { FaCss3Alt } from "react-icons/fa";
import { IoLogoJavascript } from "react-icons/io5";
import { SiTypescript } from "react-icons/si";
import { FaReact } from "react-icons/fa";
import { RiTailwindCssFill } from "react-icons/ri";
import { RiNextjsFill } from "react-icons/ri";
import { FaPython } from "react-icons/fa";
import { SiDjango } from "react-icons/si";
import { SiFastapi } from "react-icons/si";
import { TbBrandCSharp } from "react-icons/tb";
import { SiDotnet } from "react-icons/si";
import { FaNodeJs } from "react-icons/fa";
import { SiExpress } from "react-icons/si";
import { DiMongodb } from "react-icons/di";
import { DiSqllite } from "react-icons/di";
import { SiPostgresql } from "react-icons/si";
import { FaGithub } from "react-icons/fa";
import { SiSwagger } from "react-icons/si";
import { SiPostman } from "react-icons/si";
import { SiNetlify } from "react-icons/si";
import { IoLogoVercel } from "react-icons/io5";
import { VscVscode } from "react-icons/vsc";
import { FaBootstrap } from "react-icons/fa";




const skills = [
  // Frontend
  { name: "HTML", level: 95, category: "frontend", logo : <FaHtml5/>},
  { name: "CSS", level: 95, category: "frontend", logo : <FaCss3Alt/> },
  { name: "Tailwind CSS", level: 90, category: "frontend", logo : <RiTailwindCssFill/> },
  { name : "Bootstrap", level: 75, category: "frontend", logo : <FaBootstrap/> },
  { name: "JavaScript", level: 90, category: "frontend", logo : <IoLogoJavascript/> },
  { name: "TypeScript", level: 85, category: "frontend", logo : <SiTypescript/> },
  { name: "React", level: 90, category: "frontend", logo : <FaReact/> },
  { name: "Next.js", level: 80, category: "frontend", logo : <RiNextjsFill/> },


  // Backend
  { name: "Python", level: 80, category: "backend", logo : <FaPython/> },
  { name: "Typescript", level: 80, category: "backend", logo : <SiTypescript/> },
  { name: "C#", level: 80, category: "backend", logo : <TbBrandCSharp/> },
  { name: "Django", level: 80, category: "backend", logo : <SiDjango/> },
  { name: "Fast API", level: 80, category: "backend", logo : <SiFastapi/> },
  { name: ".Net", level: 80, category: "backend", logo : <SiDotnet/> },
  { name: "Node.js", level: 80, category: "backend", logo : <FaNodeJs/> },
  { name: "Express", level: 75, category: "backend", logo : <SiExpress/> },
  { name: "MongoDB", level: 70, category: "backend", logo : <DiMongodb/> },
  { name: "PostgreSQL", level: 65, category: "backend", logo : <SiPostgresql/> },
  { name: "Sqlite", level: 60, category: "backend", logo : <DiSqllite/> },

  // Tools
  { name: "Git/GitHub", level: 90, category: "tools", logo : <FaGithub/> },
  { name: "Swagger", level: 85, category: "tools", logo : <SiSwagger/> },
  { name: "Postman", level: 85, category: "tools", logo : <SiPostman/> },
  { name: "Netlify", level: 85, category: "tools", logo : <SiNetlify/> },
  { name: "Vercel", level: 85, category: "tools", logo : <IoLogoVercel/> },
  { name: "VS Code", level: 95, category: "tools", logo : <VscVscode/> },
];

const categories = ["all", "frontend", "backend", "tools"];

export const SkillsSection = () => {
  const [activeCategory, setActiveCategory] = useState("all");

  const filteredSkills = skills.filter(
    skill => activeCategory === "all" || skill.category === activeCategory
  );
  return (
    <section id="skills" className="py-24 px-4 relative bg-secondary/30">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">
          MY <span className="text-blue-500"> SKILLS</span>
        </h2>

        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category, key) => (
            <button
              key={key}
              onClick={() => setActiveCategory(category)}
              className={cn(
                "px-5 py-2 rounded-full transition-colors duration-300 capitalize cursor-pointer",
                activeCategory === category
                  ? "bg-blue-500 text-primary-foreground"
                  : "bg-secondary/70 text-forefround hover:bd-secondary"
              )}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSkills.map((skill , key) => (
            <div
              key={key}
              className="bg-card p-6 rounded-lg shadow-xs card-hover"
            >
              <div className="flex justify-between mb-4">
                <h3 className="font-semibold text-lg"> {skill.name}</h3>
                <span className="text-white text-2xl">{skill.logo}</span>
              </div>
              <div className="w-full bg-secondary/50 h-2 rounded-full overflow-hidden">
                <div
                  className="bg-primary h-2 rounded-full origin-left animate-[grow_1.5s_ease-out]"
                  style={{ width: skill.level + "%" }}
                />
              </div>

              <div className="text-right mt-1">
                <span className="text-sm text-muted-foreground">
                  {skill.level}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
