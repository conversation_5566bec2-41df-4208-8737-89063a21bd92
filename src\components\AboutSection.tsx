import { Server, Code, User,DatabaseZap } from "lucide-react";
<Server />

export const AboutSection = () => {
  return (
    <section id="about" className="py-24 px-4 relative">
      {" "}
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">
          ABOUT <span className="text-blue-500"> ME</span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-center text-blue-400">
              Web Developer & Industrial Engineer
            </h3>

            <p className="text-muted-foreground text-center">
              <PERSON><PERSON> is an Industrial Engineer and a Full Stack Web Developer who seeks new challenges and opportunities to grow in the field of Web Development with a strong foundation in both engineering principles and modern web technologies.
            </p>

            <p className="text-muted-foreground">
              I am passionate about creating elegant solutions to complex
              problems, and I am constantly learning new technologies and
              techniques to stay at the forefront of the ever-evolving web
              landscape.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 pt-4 justify-center">
              <a href="#contact" className="cosmic-button">
                {" "}
                Get In Touch
              </a>

            </div>
          </div>

          <div className="grid grid-cols-1 gap-6">
            <div className="gradient-border p-6 card-hover">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <User className="h-6 w-6 text-blue-500" />
                </div>
                <div className="text-left">
                  <h4 className="font-semibold text-lg text-blue-500 uppercase text-center"> Front End Development</h4>
                  <p className="text-muted-foreground text-center">
                    Creating responsive websites and web applications using
                    modern front end frameworks.
                  </p>
                </div>
              </div>
            </div>
            <div className="gradient-border p-6 card-hover">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Server className="h-6 w-6 text-blue-500" />
                </div>
                <div className="text-left">
                  <h4 className="font-semibold text-lg text-blue-500 uppercase text-center">Back End Development</h4>
                  <p className="text-muted-foreground text-center">
                    Building robust and scalable server-side applications using modern back end technologies.
                  </p>
                </div>
              </div>
            </div>
            <div className="gradient-border p-6 card-hover">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <DatabaseZap className="h-6 w-6 text-blue-500" />
                </div>

                <div className="text-left">
                  <h4 className="font-semibold text-lg text-blue-500 uppercase text-center">Database Design</h4>
                  <p className="text-muted-foreground text-center">
                    Database design and management, ensuring data integrity and optimal performance using both SQL and NoSQL technologies.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
